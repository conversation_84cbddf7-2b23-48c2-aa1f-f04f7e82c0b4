from pydantic import BaseModel
from typing import List, Dict, Any, Optional

class Step(BaseModel):
    """
    Pydantic model for a single automation step.
    The fields are flexible to accommodate different action types.
    """
    action: str
    # Using Optional for parameters that might not be present for every action
    url: Optional[str] = None
    field_description: Optional[str] = None
    element_description: Optional[str] = None
    value: Optional[str] = None
    time_seconds: Optional[float] = None
    # Add any other common parameters here as Optional

class TestCase(BaseModel):
    """
    Pydantic model for creating or updating a test case.
    """
    name: str
    description: Optional[str] = ""
    natural_language_input: str # The raw natural language input for parsing

class TestResult(BaseModel):
    """
    Pydantic model for the result of a single test step execution.
    """
    action: str
    status: str
    details: Dict[str, Any] # Flexible dictionary for action-specific details