import requests
from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>, Play<PERSON>, <PERSON><PERSON><PERSON> as PlaywrightError

class BrowserManager:
    """
    Manages the Playwright browser lifecycle, including launching,
    creating contexts and pages, and closing them.
    """
    def __init__(self, headless: bool = False):
        """
        Initializes the BrowserManager.

        Args:
            headless (bool, optional): Whether to run the browser in headless mode. Defaults to False.
        """
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.headless = headless
        self.default_timeout = 30000  # 30 seconds in milliseconds

    def launch_browser(self):
        """
        Launches the Playwright browser and sets up a new page.
        """
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process',
                    '--no-sandbox',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ],
                ignore_default_args=['--enable-automation']
            )
            self.context = self.browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                viewport={'width': 1280, 'height': 720},
                ignore_https_errors=True
            )
            self.page = self.context.new_page()
            self.page.set_default_timeout(self.default_timeout)
            print("Browser launched and page created successfully.")
        except PlaywrightError as e:
            print(f"Error launching browser: {e}")
            raise

    def get_page(self) -> Page:
        """
        Returns the active Playwright page object.

        Returns:
            Page: The Playwright page object.
        """
        if not self.page:
            raise RuntimeError("Browser not launched. Call launch_browser() first.")
        return self.page

    def check_connectivity(self):
        """
        Checks internet connectivity by attempting to reach Google.
        """
        try:
            response = requests.get("https://www.google.com", timeout=10)
            if response.status_code != 200:
                print("WARNING: Internet connection check failed. Status code:", response.status_code)
        except requests.RequestException as e:
            print(f"WARNING: Internet connectivity issue detected: {e}")
            print("The browser may have trouble accessing websites.")

    def test_browser_connectivity(self):
        """
        Tests if the launched browser can access a reliable website (example.com).
        """
        if not self.page:
            print("Browser not launched, skipping browser connectivity test.")
            return

        try:
            print("Testing browser connectivity...")
            self.page.goto("https://www.example.com",
                           wait_until="domcontentloaded",
                           timeout=self.default_timeout)
            print("Browser connectivity test successful!")
        except PlaywrightError as e:
            print(f"WARNING: Browser connectivity test failed: {e}")
            print("This may indicate network, proxy, or browser configuration issues.")
            print("Continuing anyway, but web access may be limited.")

    def close(self):
        """
        Closes the Playwright page, context, browser, and stops Playwright.
        """
        if self.page:
            self.page.close()
        if self.context:
            self.context.close()
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()
        print("Browser and Playwright closed.")