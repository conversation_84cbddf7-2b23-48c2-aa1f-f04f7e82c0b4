import os
import openai
from typing import Dict, Any

class LLMService:
    """
    Manages interactions with OpenAI API or Azure OpenAI, including API key handling,
    model selection, and tracking token usage.
    """
    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo",
                 use_azure: bool = None, azure_endpoint: str = None,
                 azure_deployment: str = None, api_version: str = "2024-02-15-preview"):
        """
        Initializes the LLMService for OpenAI or Azure OpenAI.

        Args:
            api_key (str, optional): API key. For OpenAI: OPENAI_API_KEY, For Azure: AZURE_OPENAI_API_KEY
            model (str, optional): The model to use. Defaults to "gpt-3.5-turbo".
            use_azure (bool, optional): Whether to use Azure OpenAI. Auto-detected if None.
            azure_endpoint (str, optional): Azure OpenAI endpoint. Gets from AZURE_OPENAI_ENDPOINT if None.
            azure_deployment (str, optional): Azure deployment name. Gets from AZURE_OPENAI_DEPLOYMENT if None.
            api_version (str, optional): Azure API version. Defaults to "2024-02-15-preview".
        """
        # Auto-detect Azure usage if not specified
        if use_azure is None:
            use_azure = bool(os.environ.get("AZURE_OPENAI_ENDPOINT") or os.environ.get("AZURE_OPENAI_API_KEY"))

        self.use_azure = use_azure
        self.model = model
        self.token_usage = 0

        if self.use_azure:
            # Azure OpenAI configuration
            self.api_key = api_key or os.environ.get("AZURE_OPENAI_API_KEY")
            self.azure_endpoint = azure_endpoint or os.environ.get("AZURE_OPENAI_ENDPOINT")
            self.azure_deployment = azure_deployment or os.environ.get("AZURE_OPENAI_DEPLOYMENT")
            self.api_version = api_version

            if not self.api_key:
                raise ValueError(
                    "Azure OpenAI API key is required. Set it via AZURE_OPENAI_API_KEY environment variable or pass it as api_key."
                )
            if not self.azure_endpoint:
                raise ValueError(
                    "Azure OpenAI endpoint is required. Set it via AZURE_OPENAI_ENDPOINT environment variable or pass it as azure_endpoint."
                )
            if not self.azure_deployment:
                raise ValueError(
                    "Azure OpenAI deployment is required. Set it via AZURE_OPENAI_DEPLOYMENT environment variable or pass it as azure_deployment."
                )

            self.client = openai.AzureOpenAI(
                api_key=self.api_key,
                azure_endpoint=self.azure_endpoint,
                api_version=self.api_version
            )
            print(f"[LLM] Initialized Azure OpenAI with endpoint: {self.azure_endpoint}")
            print(f"[LLM] Using deployment: {self.azure_deployment}")
        else:
            # Standard OpenAI configuration
            self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
            if not self.api_key:
                raise ValueError(
                    "OpenAI API key is required. Set it via OPENAI_API_KEY environment variable or pass it as api_key."
                )

            self.client = openai.OpenAI(api_key=self.api_key)
            print(f"[LLM] Initialized OpenAI with model: {self.model}")

    def generate_content(self, prompt: str, response_format: Dict[str, str] = None) -> str:
        """
        Generates content using the configured OpenAI or Azure OpenAI model.

        Args:
            prompt (str): The prompt to send to the LLM.
            response_format (Dict[str, str], optional): Specifies the desired response format (e.g., {"type": "json_object"}).
                                                        Defaults to None for plain text.

        Returns:
            str: The generated content from the LLM.
        """
        messages = [{"role": "user", "content": prompt}]

        try:
            # Determine which model/deployment to use
            model_name = self.azure_deployment if self.use_azure else self.model

            if response_format:
                response = self.client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=0.1,
                    response_format=response_format
                )
            else:
                response = self.client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=0.1
                )

            if hasattr(response, 'usage') and response.usage:
                self.token_usage += response.usage.total_tokens

            return response.choices[0].message.content
        except openai.APIError as e:
            service_type = "Azure OpenAI" if self.use_azure else "OpenAI"
            print(f"{service_type} API error: {e}")
            raise
        except Exception as e:
            service_type = "Azure OpenAI" if self.use_azure else "OpenAI"
            print(f"An unexpected error occurred during {service_type} content generation: {e}")
            raise

    def get_total_token_usage(self) -> int:
        """
        Returns the total token usage accumulated by this service instance.

        Returns:
            int: Total tokens used.
        """
        return self.token_usage