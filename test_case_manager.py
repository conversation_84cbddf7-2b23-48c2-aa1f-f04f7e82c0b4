import json
import uuid
from datetime import datetime
from typing import Dict, Any, List

class TestCaseManager:
    """
    Manages the storage and retrieval of test cases from a JSON file.
    Handles loading, saving, adding, updating, and deleting test cases.
    """
    def __init__(self, file_path: str = "test_cases.json"):
        """
        Initializes the TestCaseManager.

        Args:
            file_path (str, optional): The path to the JSON file where test cases are stored.
                                       Defaults to "test_cases.json".
        """
        self.file_path = file_path
        self.test_cases = self._load_test_cases()

    def _load_test_cases(self) -> Dict[str, Any]:
        """
        Loads test cases from the JSON file.

        Returns:
            Dict[str, Any]: A dictionary of test cases, keyed by their IDs.
        """
        try:
            with open(self.file_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
        except json.JSONDecodeError:
            print(f"Warning: {self.file_path} is corrupted or empty. Starting with empty test cases.")
            return {}

    def _save_test_cases(self):
        """
        Saves the current state of test cases to the JSON file.
        """
        with open(self.file_path, "w") as f:
            json.dump(self.test_cases, f, indent=4)

    def get_all_test_cases(self) -> Dict[str, Any]:
        """
        Returns all stored test cases.

        Returns:
            Dict[str, Any]: A dictionary of all test cases.
        """
        return self.test_cases

    def get_test_case(self, test_id: str) -> Dict[str, Any]:
        """
        Retrieves a specific test case by its ID.

        Args:
            test_id (str): The ID of the test case to retrieve.

        Returns:
            Dict[str, Any]: The test case dictionary.

        Raises:
            ValueError: If the test case with the given ID is not found.
        """
        test_case = self.test_cases.get(test_id)
        if not test_case:
            raise ValueError(f"Test case with ID '{test_id}' not found.")
        return test_case

    def add_test_case(self, name: str, description: str, natural_language_input: str, steps: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Adds a new test case.

        Args:
            name (str): The name of the test case.
            description (str): The description of the test case.
            natural_language_input (str): The original natural language input.
            steps (List[Dict[str, Any]]): The parsed structured steps for the test case.

        Returns:
            Dict[str, Any]: The newly created test case dictionary.

        Raises:
            ValueError: If a test case with the same name already exists.
        """
        for existing_test_id, existing_test in self.test_cases.items():
            if existing_test["name"] == name:
                raise ValueError(f"Test case with name '{name}' already exists.")

        test_id = str(uuid.uuid4())
        new_test_case = {
            "id": test_id,
            "name": name,
            "description": description,
            "natural_language_input": natural_language_input,
            "steps": steps,
            "created_at": datetime.now().isoformat(),
            "last_run": None,
            "run_count": 0
        }
        self.test_cases[test_id] = new_test_case
        self._save_test_cases()
        return new_test_case

    def update_test_case(self, test_id: str, updates: Dict[str, Any]):
        """
        Updates an existing test case.

        Args:
            test_id (str): The ID of the test case to update.
            updates (Dict[str, Any]): A dictionary of fields to update.

        Raises:
            ValueError: If the test case with the given ID is not found.
        """
        if test_id not in self.test_cases:
            raise ValueError(f"Test case with ID '{test_id}' not found.")

        # Prevent updating the ID itself
        if "id" in updates:
            del updates["id"]

        self.test_cases[test_id].update(updates)
        self._save_test_cases()

    def delete_test_case(self, test_id: str):
        """
        Deletes a test case by its ID.

        Args:
            test_id (str): The ID of the test case to delete.

        Raises:
            ValueError: If the test case with the given ID is not found.
        """
        if test_id not in self.test_cases:
            raise ValueError(f"Test case with ID '{test_id}' not found.")
        del self.test_cases[test_id]
        self._save_test_cases()

    def increment_run_count(self, test_id: str):
        """
        Increments the run count and updates the last run time for a test case.

        Args:
            test_id (str): The ID of the test case.

        Raises:
            ValueError: If the test case with the given ID is not found.
        """
        if test_id not in self.test_cases:
            raise ValueError(f"Test case with ID '{test_id}' not found.")
        self.test_cases[test_id]["run_count"] = self.test_cases[test_id].get("run_count", 0) + 1
        self.test_cases[test_id]["last_run"] = datetime.now().isoformat()
        self._save_test_cases()

