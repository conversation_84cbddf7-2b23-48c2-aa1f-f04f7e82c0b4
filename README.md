# WindQ - AI-Powered QA Automation

WindQ is an AI-powered QA automation tool that enables users to create, run, and maintain end-to-end tests using natural language. The platform leverages AI to interpret user instructions, execute tests in real browsers, and minimize test maintenance overhead.

## Project Structure

The project is organized into several key Python modules:

*   **`main.py`**: The main entry point for the WindQ application.
*   **`llm_service.py`**: <PERSON>les interactions with the Large Language Model (LLM) for interpreting natural language instructions.
*   **`browser_manager.py`**: Manages browser instances (e.g., launching, closing, and interacting with web pages).
*   **`auto_playwright.py`**: Auto-playwright implementation for natural language web automation using dynamic actions.
*   **`instruction_parser.py`**: Parses the natural language instructions into actionable commands for the automation engine.
*   **`action_executor.py`**: Executes all commands using auto-playwright for consistent natural language processing.
*   **`automation_orchestrator.py`**: Coordinates the overall test execution flow, integrating the various components.
*   **`models.py`**: Defines data structures and models used throughout the application (e.g., for test cases, test steps).
*   **`test_case_manager.py`**: Manages the storage, retrieval, and organization of test cases.
*   **`test_runner.py`**: Responsible for executing test cases and reporting results.
*   **`requirements.txt`**: Lists the Python dependencies required for the project.
*   **`.env`**: Configuration file for environment variables (e.g., API keys).
*   **`test_cases.json`**: Stores test case definitions in JSON format.
*   **`data/`**: Directory for storing test run data, such as screenshots.

## 🚀 Ultra-Simple Auto-Playwright Integration

WindQ now uses **auto-playwright** for ultimate simplicity. No more complex action types - just describe what you want to do in natural language!

### Revolutionary Approach

**Traditional approach (complex):**
```json
{
  "action": "click",
  "element_description": "login button",
  "selector": "#login-btn",
  "wait_for_element": true
}
```

**WindQ with auto-playwright (simple):**
```json
{
  "natural_language": "Click the login button"
}
```

### Supported Input Formats

Auto-playwright understands multiple input formats and converts them to natural language:

```json
// Pure natural language (recommended)
{"natural_language": "Fill the email field with '<EMAIL>'"}

// Structured data (auto-converted)
{"url": "https://example.com"}  // → "Go to https://example.com"
{"field_description": "email", "value": "<EMAIL>"}  // → "Fill the email with '<EMAIL>'"
{"element_description": "submit button"}  // → "Click the submit button"

// Description field
{"description": "Navigate to Google and search for playwright"}

// Any combination works!
```

### Example Test Case

```json
{
  "name": "Ultra Simple Login Test",
  "steps": [
    {"natural_language": "Go to https://example.com/login"},
    {"natural_language": "Fill the username field with 'testuser'"},
    {"natural_language": "Fill the password field with 'password123'"},
    {"natural_language": "Click the login button"},
    {"natural_language": "Verify that the dashboard is visible"}
  ]
}
```

## Getting Started

To get WindQ up and running, follow these steps:

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd windq
    ```

2.  **Create and activate a virtual environment:**
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
    ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
    This will install all necessary packages including:
    *   `fastapi`
    *   `uvicorn`
    *   `playwright`
    *   `openai`
    *   `requests`
    *   `python-dotenv`
    *   `pydantic`

4.  **Set up environment variables:**
    Create a `.env` file in the root directory. You can use either OpenAI or Azure OpenAI:

    **Option A: Azure OpenAI (Recommended for Enterprise)**
    ```env
    AZURE_OPENAI_API_KEY="your_azure_openai_api_key_here"
    AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
    AZURE_OPENAI_DEPLOYMENT="your-deployment-name"
    ```

    **Option B: Standard OpenAI**
    ```env
    OPENAI_API_KEY="your_openai_api_key_here"
    ```

    📖 **For detailed Azure OpenAI setup instructions, see [AZURE_OPENAI_SETUP.md](AZURE_OPENAI_SETUP.md)**

5.  **Install Playwright browsers:**
    Playwright needs to download browser binaries. Run the following command:
    ```bash
    playwright install
    ```

6.  **Run the application:**
    The application is likely started using Uvicorn with `main.py`.
    ```bash
    uvicorn main:app --reload
    ```
    (Verify the exact command if `main.py` uses a FastAPI app instance named `app`)

    The application should now be running, typically at `http://127.0.0.1:8000`.

## Contributing

We welcome contributions to WindQ! Please follow these general guidelines:

1.  **Fork the repository.**
2.  **Create a new branch** for your feature or bug fix: `git checkout -b feature/your-feature-name` or `bugfix/issue-number`.
3.  **Make your changes** and commit them with clear, descriptive messages.
4.  **Ensure your code adheres to the project's coding standards.** (Details to be added)
5.  **Write or update tests** for your changes.
6.  **Push your branch** to your fork: `git push origin feature/your-feature-name`.
7.  **Open a pull request** against the main WindQ repository.

Further details on coding standards, testing procedures, and the development workflow will be added here.

## License

This project is currently under Spurtree technologies.