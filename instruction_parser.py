import json
import re
from typing import List, Dict, Any
from llm_service import LLMService


class InstructionParser:
    """
    Parses natural language test automation instructions into a structured
    JSON format using an LLM.
    """

    def __init__(self, llm_service: LLMService):
        """
        Initializes the InstructionParser.

        Args:
            llm_service (LLMService): An instance of LLMService for LLM calls.
        """
        self.llm_service = llm_service

    def parse_instructions(self, actions_text: str) -> List[Dict[str, Any]]:
        """
        Parses a block of natural language instructions into a list of structured
        action dictionaries.

        Args:
            actions_text (str): A string containing natural language test instructions.

        Returns:
            List[Dict[str, Any]]: A list of dictionaries, where each dictionary
                                  represents a parsed action with its parameters.
        """
        prompt = f"""
        Parse the following test automation instructions into a structured JSON array.
        For each instruction, identify the action type and relevant parameters.

        Instructions:
        {actions_text}

        Return a JSON array where each object represents a single instruction with the following format:
        [
            {{
                "action": "goto",
                "url": "https://example.com"
            }},
            {{
                "action": "fill",
                "field_description": "Email input field",
                "value": "<EMAIL>"
            }},
            {{
                "action": "click",
                "element_description": "Submit button"
            }},
            {{
                "action": "wait_for",
                "element_description": "Dashboard header",
                "timeout": 10
            }},
            {{
                "action": "wait",
                "time_seconds": 5
            }},
            {{
                "action": "select",
                "element_description": "Country dropdown",
                "value": "USA"
            }},
            {{
                "action": "hover",
                "element_description": "Profile icon"
            }},
            {{
                "action": "check",
                "element_description": "Remember me checkbox"
            }},
            {{
                "action": "uncheck",
                "element_description": "Email notifications checkbox"
            }}
        ]

        Only include these actions: goto, fill, click, wait, wait_for, select, hover, check, uncheck.
        The 'wait_for' action waits for an element to appear on the page.
        Ensure 'field_description' or 'element_description' are descriptive.
        Respond with valid JSON only.
        """

        parsed_content = self.llm_service.generate_content(prompt, response_format={"type": "json_object"})
        print(f"LLM Raw Parse Response: {parsed_content}")

        parsed_steps = []
        try:
            parsed_json = json.loads(parsed_content)
            # Handle cases where LLM might wrap the array in an object
            if isinstance(parsed_json, dict) and (parsed_json.get('instructions') or parsed_json.get('actions')):
                parsed_steps = parsed_json.get('instructions', []) or parsed_json.get('actions', [])
            elif isinstance(parsed_json, list):
                parsed_steps = parsed_json
            else:
                raise ValueError("LLM response is not a valid JSON array or an object containing one.")
        except json.JSONDecodeError:
            print("JSONDecodeError: Attempting to extract JSON from raw LLM response.")
            match = re.search(r'\[.*\]', parsed_content, re.DOTALL)
            if match:
                try:
                    parsed_steps = json.loads(match.group(0))
                except json.JSONDecodeError as e_inner:
                    print(f"Failed to extract and parse JSON from LLM response: {e_inner}")
                    print(f"Raw LLM response: {parsed_content}")
                    return []  # Return empty list on failure
            else:
                print("No JSON array found in LLM response.")
                print(f"Raw LLM response: {parsed_content}")
                return []  # Return empty list on failure
        except Exception as e_outer:
            print(f"An unexpected error occurred during instruction parsing: {e_outer}")
            print(f"Raw LLM response: {parsed_content}")
            return []  # Return empty list on failure

        # Manually add detected_selector to relevant steps
        actions_needing_selector = ["fill", "click", "wait_for", "select", "hover", "check", "uncheck"]
        for step in parsed_steps:
            if isinstance(step, dict) and step.get("action") in actions_needing_selector:
                step["detected_selector"] = {"strategy": None, "value": None}

        return parsed_steps

